import { sleep } from '../../utils/common';
import { BrowserAgent } from '../browser';
import { Context } from '../context';
import { v4 as uuidv4 } from 'uuid';
import type { AiResponse } from '../interface/ai-response';
import type { AiMessage } from '../interface/ai-message';
import type { HumanMessage } from '../interface/human-message';

interface Task {
    id: string;
    context: Context;
}

export class TaskManager {
    private static instance: TaskManager;
    private contexts: Task[] = [];
    private browserAgent: BrowserAgent;
    private taskId: string = '';
    private initialized: boolean = false;

    private safeAttributes: string[] = [
        'id',
        'name',
        'type',
        'placeholder',
        'aria-label',
        'aria-labelledby',
        'aria-describedby',
        'role',
        'for',
        'autocomplete',
        'required',
        'readonly',
        'alt',
        'title',
        'src',
        'href',
        'target',
    ];

    private dynamicAttributes: string[] = [
        'data-id',
        'data-qa',
        'data-cy',
        'data-testid',
    ];

    private constructor() {
    }

    public static getInstance() {
        if (!TaskManager.instance) {
            this.instance = new TaskManager();
        }
        return TaskManager.instance;
    }

    async init() {
        if (this.initialized) {
            return; // 避免重复初始化
        }

        const browserAgent = new BrowserAgent();
        await browserAgent.init({
            cdpUrl: 'http://localhost:9222',
            remoteDebuggingPort: 9222,
            userDataDir: '',
            disableSecurity: true,
            extraChromeArgs: [],
            headless: true,
            chromeInstancePath: '',
        });
        this.browserAgent = browserAgent;

        // 检查Chrome是否已经在运行，如果没有则启动
        const isRunning = await this.browserAgent.verifyChromeLaunched();
        if (!isRunning) {
            await this.browserAgent.startChromeProcess();
            await sleep(1000);
        }

        this.initialized = true;
    }

    async createTask(data: any) {
        try {
            const { userPrompt }: { userPrompt: string } = data;

            // 确保TaskManager已初始化
            if (!this.initialized) {
                await this.init();
            }

            const context = new Context();
            await context.init(this.browserAgent);
            const contextResult = await context.createContext();

            if (!contextResult) {
                throw new Error('Failed to create browser context. Browser may not be initialized properly.');
            }

            const id = uuidv4();
            context.currentStep = 1;
            context.currentDateTime = new Date().toISOString();
            context.task = userPrompt;
            context.memory = '';
            context.previousAction = '';
            context.hasPixelsAbove = 0;
            context.hasPixelsBelow = 0;
            context.totalSteps = 100;
            this.taskId = id;
            this.contexts.push({
                id,
                context,
            });

            // 异步生成humanMessages，避免阻塞响应
            const humanMessages = await this.convertHumanMessage(context, '');
            return {
                id: id,
                prompt: JSON.stringify({
                    AIMessages: [],
                    HumanMessages: humanMessages,
                }),
            };
        } catch (error) {
            console.error('Error creating task:', error);
            throw error;
        }
    }

    async executeTask(data: any) {
        const { id, actions, aiResponse }: { id: string, actions: any[], aiResponse: AiResponse} = data;
        const task = this.contexts.find(task => task.id === id);
        if (!task) {
            throw new Error('Task not found');
        }
        const aiMessage = await this.convertAiMessage(aiResponse);
        if (aiMessage) {
            task.context.aiMessages.push(aiMessage);
        }
        task.context.currentStep = task.context.currentStep + 1;
        let extractedContent = '';
        for (const action of actions) {
            console.log('Executing action', action);
            if (action.type === 'go_to_url') {
                await task.context.navigateTo(action.url);
                console.log('Navigated to', action.url);
            } else if (action.type === 'input_text') {
                await task.context.inputTextElementNode(action.index, action.text);
                console.log('Input text', action.text);
            } else if (action.type === 'click_element') {
                await task.context.clickElementNode(action.index);
                console.log('Clicked element', action.index);
            } else if (action.type === 'scroll_down') {
                await task.context.scrollDown(action.amount);
                console.log('Scrolled down');
            } else if (action.type === 'scroll_up') {
                await task.context.scrollUp(action.amount);
                console.log('Scrolled up');
            } else if (action.type === 'extract_content') {
                extractedContent = await task.context.extractContent(action.index);
                console.log('Extracted content');
            } else if (action.type === 'send_keys') {
                await task.context.sendKeys(action.keys);
                console.log('Sent keys', action.keys);
            } else if (action.type === 'wait') {
                await task.context.wait(action.value);
                console.log('Waited for', action.value);
            }
        }
        await task.context.resetActiveElements();
        await task.context.updateContextState();

        const humanMessages = await this.convertHumanMessage(task.context, extractedContent);
        return {
            prompt: JSON.stringify({
                id: this.taskId,
                AIMessages: task.context.aiMessages,
                HumanMessages: humanMessages,
            }),
        };
    }

    async convertAiMessage(message: AiResponse) {
        const aiMessage: AiMessage = {
            content: {
                currentState: message.currentState,
                action: message.action
            },
            additionalKwargs: {},
            responseMetadata: {},
            id: uuidv4(),
            usageMetadata: {
                promptTokens: message.usage.promptTokens,
                completionTokens: message.usage.completionTokens,
                totalTokens: message.usage.totalTokens
            }
        }
        return aiMessage
    }

    async convertHumanMessage(context: Context, extractedContent: string) {

        const humanMessages: HumanMessage[] = [];

        const humanStructTextMessage = {
            stepIndex: '',
            executionTime: '',
            task: '',
            hints: '\n\n',
            memory: '',
            url: '',
            tabs: '',
            elementText: '',
            previousAction: ''
        }


        let textFormattedElements = await context.getActiveElementString(this.safeAttributes);

        humanStructTextMessage.elementText += `[Start of page]\n\n${textFormattedElements}\n\n[End of page]`;
        
        if (textFormattedElements.length === 0) {
            humanStructTextMessage.elementText = 'emply page';
        }
        humanStructTextMessage.stepIndex = `Current Step ${context.currentStep} of ${context.totalSteps}`;
        humanStructTextMessage.executionTime = context.currentDateTime;
        humanStructTextMessage.task = context.task;
        humanStructTextMessage.memory = context.memory;
        humanStructTextMessage.url = context.currentUrl;
        humanStructTextMessage.tabs = context.availableTabs;
        humanStructTextMessage.previousAction = context.previousAction;
        

        const humenTextMessageArray = [
            humanStructTextMessage.stepIndex,
            `1. Task: ${humanStructTextMessage.task}`,
            `2. Hints(Optional): ${humanStructTextMessage.hints}`,
            `3. Memory:`,
            `${humanStructTextMessage.memory}`,
            `4. Current URL: ${humanStructTextMessage.url}`,
            `5. Available Tabs:`,
            `${humanStructTextMessage.tabs}`,
            `6. Interactive Elements:`,
            `${humanStructTextMessage.elementText}`,
            `${humanStructTextMessage.previousAction}`,
            `7. Extracted Content:`,
        ]

        if (extractedContent.length > 0) {
            humenTextMessageArray.push(`[Start of extracted content]`,
                `${extractedContent}`,
                `[End of extracted content]`
            );
        }

        const humanTextMessage = {
            type: 'text',
            text: humenTextMessageArray.join("\n")
        }

        // const screenshot = await context.takeScreenshot(true);
        const imageUrl = '';
        const humanImageMessage = {
            type: 'image',
            imageUrl: imageUrl
        }
        humanMessages.push(humanTextMessage, humanImageMessage);

        return humanMessages;
    }

    async getHumanPromptList(data: any) {
        const { id }: { id: string } = data;
        const task = this.contexts.find(task => task.id === id);
        if (!task) {
            throw new Error('Task not found');
        }
        const result = await task.context.getActiveElements();
        return result;
    }

    async takeScreenshot(data: any) {
        const { id, fullPage = true }: { id: string, fullPage?: boolean } = data;
        const task = this.contexts.find(task => task.id === id);
        if (!task) {
            throw new Error('Task not found');
        }
        const screenshot = await task.context.takeScreenshot(fullPage);
        return {
            screenshot: screenshot,
            timestamp: new Date().toISOString(),
            url: task.context.currentUrl
        };
    }

    async getPageInfo(data: any) {
        const { id }: { id: string } = data;
        const task = this.contexts.find(task => task.id === id);
        if (!task) {
            throw new Error('Task not found');
        }
        const pageInfo = await task.context.getDetailedPageInfo();
        return pageInfo;
    }

    async extractAllContent(data: any) {
        const { id }: { id: string } = data;
        const task = this.contexts.find(task => task.id === id);
        if (!task) {
            throw new Error('Task not found');
        }
        const allContent = await task.context.extractAllPageContent();
        return allContent;
    }

    async searchText(data: any) {
        const { id, searchTerm, caseSensitive = false }: { id: string, searchTerm: string, caseSensitive?: boolean } = data;
        const task = this.contexts.find(task => task.id === id);
        if (!task) {
            throw new Error('Task not found');
        }
        const searchResults = await task.context.searchTextOnPage(searchTerm, caseSensitive);
        return searchResults;
    }
}