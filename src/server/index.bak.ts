import http, { IncomingMessage, ServerResponse } from 'http';
import { BrowserAgent } from './browser';
import { Context } from './context';
import fs from 'fs';
import path from 'path';
const PORT = 3000;


type Handler = (req: IncomingMessage, res: ServerResponse) => Promise<void>;
type RouteMap = Record<string, Handler>;
// 安全获取全局 fetch（避免类型环境不包含 DOM 时的编译报错）
const gfetch = (globalThis as any).fetch as
  | ((input: string) => Promise<{ json(): Promise<unknown> }>)
  | undefined;
const PUBLIC_DIR = path.join(process.cwd(), 'public');

const browserAgent = new BrowserAgent();
browserAgent.init({
  cdpUrl: 'http://localhost:9222',
  remoteDebuggingPort: 9222,
  userDataDir: '',
  disableSecurity: true,
  extraChromeArgs: [],
  headless: true,
  chromeInstancePath: '',
});

const routes: RouteMap = {
    '/': async (_req, res) => {
        sendJson(res, 200, { info: 'chrome started' });
        browserAgent.startChromeProcess();
    },
    '/createTask': async (_req, res) => {
      sendJson(res, 200, { info: 'task created' });  
      const context = new Context();
      context.init(browserAgent);
      await context.createContext();
      await context.navigateTo('https://h.liepin.com/search/getConditionItem');
    },
    '/close': async (_req, res) => {
      sendJson(res, 200, { error: 'chrome closed' });
      browserAgent.closeChromeProcess();
    }
};

const startHttpServer = () => {
    http.createServer(async (req: IncomingMessage, res: ServerResponse) => {
      try {
          const path = normalizePath(req.url);
          if (await tryServeHtml(path, res)) return;
          const handler = routes[path];
          if (handler) {
            await handler(req, res);
          } else {
            sendJson(res, 404, { error: 'Not Found' });
          }
        } catch (err) {
          sendJson(res, 500, { error: (err as Error).message });
        }
      })
      .listen(PORT, () => {
        console.log(`Server running at http://localhost:${PORT}`);
      });
}

// ------- helpers -------
const normalizePath = (u?: string | null): string => {
  if (!u) return '/';
  const i = u.indexOf('?');
  return (i >= 0 ? u.slice(0, i) : u) || '/';
}


const sendJson = (res: ServerResponse, status: number, body: unknown) => {
  res.statusCode = status;
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  res.end(JSON.stringify(body));
}


const tryServeHtml = async (requestPath: string, res: ServerResponse): Promise<boolean> => {
  const safeFilePath = resolveHtmlPath(requestPath);
  if (!safeFilePath) return false;
  try {
    const data = await fs.promises.readFile(safeFilePath);
    res.statusCode = 200;
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.end(data);
    return true;
  } catch {
    return false;
  }
}


const resolveHtmlPath = (requestPath: string): string | null => {
  const trimmed = requestPath || '/';
  const target = trimmed === '/' ? 'index.html' : trimmed.replace(/^\//, '');
  if (!target.endsWith('.html')) return null;
  const abs = path.join(PUBLIC_DIR, target);
  // Prevent path traversal outside PUBLIC_DIR
  if (!abs.startsWith(PUBLIC_DIR)) return null;
  return abs;
}

export { startHttpServer };