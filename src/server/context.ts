import { Page, FrameLocator, BrowserContext as PlaywrightContext } from 'playwright';
import { BrowserAgent } from './browser';
import { buildDomTree } from './dom/buildDomTree';
import { sleep } from '../utils/common';
import type { AiMessage } from './interface/ai-message';
import type { HumanMessage } from './interface/human-message';

interface DomElementNode {
    highlightIndex: number;
    xpath: string;
    tagName: string;
    isVisible: boolean;
    isTopElement: boolean;
    isInteractive: boolean;
    isInViewport: boolean;
    type: string;
    text: string;
    attributes: any;
    children: any[];
    childrenText: string[];
}

interface DomTree {
    map: DomElementNode[];
    rootId: string;
}

export class Context {
    browserAgent: BrowserAgent;
    context: PlaywrightContext | null = null;
    activeElements: any[] = [];
    currentPageDomTree: DomTree | null = null;
    aiMessages: any[] = [];
    humanMessages: any[] = [];
    currentStep: number = 1;
    totalSteps: number = 100;
    currentDateTime: string = '';
    task: string = '';
    memory: string = '';
    currentUrl: string = '';
    availableTabs: string = '';
    previousAction: string = '';
    hasPixelsAbove: number = 0;
    hasPixelsBelow: number = 0;

    private safeAttributes: string[] = [
        'id',
        'name',
        'type',
        'placeholder',
        'aria-label',
        'aria-labelledby',
        'aria-describedby',
        'role',
        'for',
        'autocomplete',
        'required',
        'readonly',
        'alt',
        'title',
        'src',
        'href',
        'target',
    ];

    private dynamicAttributes: string[] = [
        'data-id',
        'data-qa',
        'data-cy',
        'data-testid',
    ];

    async init(browserAgent: BrowserAgent) {
        this.browserAgent = browserAgent;
    }

    async createContext() {
        if (!this.browserAgent.browser) {
            console.log('Browser not initialized');
            return null;
        }
        if (this.browserAgent.browser.contexts().length > 0) {
            this.context = this.browserAgent.browser.contexts()[0];
        } else {
            this.context = await this.browserAgent.browser.newContext();
        }
        await this.context.tracing.start({
            screenshots: true,
            snapshots: true,
            sources: true,
        })
        return this.context;
    }

    async getCurrentPage() {
        if (!this.context) {
            console.log('Context not initialized');
            return null;
        }
        return this.context.pages()[0];
    }

    async waitForLoadState() {
        await sleep(3000);
    }

    async waitForStatbleNetwork() {
        
    }

    async waitForPageAndFramesLoad() {
       
    }

    async updateContextState() {
        this.currentUrl = await this.getCurrentUrl();
        this.availableTabs = JSON.stringify(await this.getTabInfo());
        this.currentDateTime = new Date().toISOString();

        console.log('AI Messages', this.aiMessages);
        if (this.aiMessages.length > 0) {
            const lastAiMessage: AiMessage = this.aiMessages[this.aiMessages.length - 1];
            this.memory = lastAiMessage.content.currentState.importantContents;
        }
    }

    async getActiveElements() {
        return this.activeElements;
    }

    async getFormatedActiveElements() {
        
    }

    async findAndMarkActiveElementsOnPage() {
        const page = await this.getCurrentPage();
        const evaluateResult = await page.evaluate(buildDomTree, {
            doHighlightElements: false,
            focusHighlightIndex: -1,
            viewportExpansion: 0,
            debugMode: false,
        });

        const nodeMap: any = evaluateResult.map;
        const rootId: any = evaluateResult.rootId;

        const allElements: DomElementNode[] = [];

        for (const key in nodeMap) {
            const node = nodeMap[key];
            const elementNode: DomElementNode = {
                highlightIndex: parseInt(key),
                xpath: node.xpath || '',
                tagName: node.tagName || '',
                isVisible: node.isVisible != undefined ? node.isVisible : false,
                isTopElement: node.isTopElement != undefined ? node.isTopElement : false,
                isInteractive: node.isInteractive != undefined ? node.isInteractive : false,
                isInViewport: node.isInViewport != undefined ? node.isInViewport : false,
                type: node.type || '',
                text: node.text || '',
                attributes: node.attributes || [],
                children: node.children || [],
                childrenText: node.childrenText || [],
            }
            for (const child of elementNode.children) {
                const childElement = allElements.find(element => element.highlightIndex === parseInt(child));
                if (childElement && childElement.text.length > 0) {
                    elementNode.childrenText.push(childElement.text);
                }
                if (childElement && childElement.childrenText.length > 0) {
                    for (const childText of childElement.childrenText) {
                        elementNode.childrenText.push(childText);
                    }
                }
            }
            allElements.push(elementNode);
        }

        this.currentPageDomTree = {
            map: allElements,
            rootId: rootId,
        }
    }

    async resetActiveElements() {
        this.activeElements = [];
        const page = await this.getCurrentPage();
        await this.findAndMarkActiveElementsOnPage();
        if (!this.currentPageDomTree) {
            console.log('No Page Dom Tree found.');
            return;
        }
        if (this.currentPageDomTree.map.length === 0) {
            console.log('No Elements found on the page.');
            return;
        }
        this.activeElements = this.currentPageDomTree.map.filter((element) => element.isInteractive && element.isVisible && element.isInViewport);
    }

    async closeContext() {
        if (this.context) {
            await this.context.close();
        }
    }

    // 以下是本程序支持的 Actions

    async navigateTo(url: string) {
        const page = await this.getCurrentPage();
        await page.goto(url);
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async refreshPage() {
        const page = await this.getCurrentPage();
        await page.reload();
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async goBack() {
        const page = await this.getCurrentPage();
        await page.goBack();
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async goForward() {
        const page = await this.getCurrentPage();
        await page.goForward();
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async getPageHtml() {
        const page = await this.getCurrentPage();
        return await page.content();
    }

    async getActiveElementString(includeAttributes: string[] = []) {
        const formattedText = [];
        for (let index = 0; index < this.activeElements.length; index++) {
            const nodeText = [];
            const nodeAttributes = [];
            const element = this.activeElements[index];

            if (element.text.length > 0) {
                nodeText.push(element.text);
            }
            if (element.childrenText.length > 0) {
                for (const childText of element.childrenText) {
                    nodeText.push(childText);
                }
            }
            if (includeAttributes.length > 0) {
                for (const attribute of includeAttributes) {
                    if (element.attributes[attribute] && element.attributes[attribute].length > 0) {
                        nodeAttributes.push(element.attributes[attribute]);
                    }
                }
            }

            const linkedText = `[${index}] <${element.tagName} ${nodeAttributes.join(';')}/> ${nodeText.join('\n')}`;
            formattedText.push(linkedText);
        }
        return formattedText.join('\n');
    }

    async takeScreenshot(fullPage: boolean = true) {
        const page = await this.getCurrentPage();
        if (!page) {
            console.log('Page not available for screenshot');
            // 返回一个空的base64图片或者抛出错误
            throw new Error('Page not available for screenshot');
        }
        await page.bringToFront();
        const screenshot = await page.screenshot({
            fullPage: fullPage,
            animations: 'disabled',
        });

        const screenshotBase64 = screenshot.toString('base64');
        return screenshotBase64;
    }

    async getParentElementNode(index: number) {
        const element = this.currentPageDomTree.map.find(element => element.children.includes(index));
        if (!element) {
            return null;
        }
        return element;
    }

    async getElementHandle(index: number) {
        let currentFrame: Page | FrameLocator = await this.getCurrentPage();
        const element = this.activeElements[index];
        if (!element) {
            console.log('Element not found');
            return;
        }
        const parents: DomElementNode[] = [];
        let currentElement = element;
        while (await this.getParentElementNode(currentElement.highlightIndex)) {
            const parent = await this.getParentElementNode(currentElement.highlightIndex);
            parents.push(parent);
        }
        parents.reverse();

        const iframes = parents.filter(parent => parent.tagName === 'iframe');
        let inIframe = false;
        for (const iframe of iframes) {
            const iframeElementSelector = this.buildBaseLocator(iframe);
            currentFrame = currentFrame.frameLocator(iframeElementSelector);
            inIframe = true;
        }

        if (inIframe) {
            return await currentFrame.locator(this.buildBaseLocator(element)).first().elementHandle();
        } else {
            const elementHandle = await currentFrame.locator(this.buildBaseLocator(element)).first().elementHandle();
            await elementHandle.scrollIntoViewIfNeeded();
            return elementHandle;
        }
    }

    async inputTextElementNode(index: number, value: string) {
        const elementHandle = await this.getElementHandle(index);
        const isContentEditable = await elementHandle.getProperty('isContentEditable');
        console.log('Is Content Editable', isContentEditable);
        if (await isContentEditable.jsonValue()) {
            await elementHandle.evaluate('el => el.textContent = ""');
            await elementHandle.type(value, { delay: 5 });
        } else {
            await elementHandle.fill(value);
        }
        this.previousAction = `Input Text [${index}]`;
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async clickElementNode(index: number) {    
        const elementHandle = await this.getElementHandle(index);
        await elementHandle.click();
        this.previousAction = `Clicked Element [${index}]`;
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async wait(seconds: number) {
        await sleep(seconds * 1000);
    }

    async getCurrentUrl() {
        const page = await this.getCurrentPage();
        return page.url();
    }

    async getTabInfo() {
        const page = await this.getCurrentPage();
        return {
            pageId: 0,
            url: await this.getCurrentUrl(),
            title: page.title()
        }
    }

    async switchTab() {

    }

    async openTab() {

    }

    async extractContent(index: number) {
        const elementHandle = await this.getElementHandle(index);
        const element = this.activeElements[index];
        
        if (!elementHandle || !element) {
            return 'No content found for the specified element.';
        }

        try {
            // Smart content extraction based on element type
            const extractedData = await elementHandle.evaluate((el, elementInfo) => {
                const tagName = elementInfo.tagName.toLowerCase();
                const result = {
                    type: tagName,
                    text: '',
                    attributes: {},
                    structuredContent: {},
                    metadata: {}
                };

                // Extract basic text content
                result.text = el.textContent?.trim() || '';
                
                // Extract important attributes
                const importantAttrs = ['id', 'class', 'name', 'href', 'src', 'alt', 'title', 'data-*'];
                for (const attr of el.attributes) {
                    if (importantAttrs.some(pattern => 
                        attr.name === pattern || attr.name.startsWith('data-'))) {
                        result.attributes[attr.name] = attr.value;
                    }
                }

                // Type-specific extraction
                switch (tagName) {
                    case 'table':
                        result.structuredContent = this.extractTableData(el);
                        break;
                    case 'form':
                        result.structuredContent = this.extractFormData(el);
                        break;
                    case 'article':
                    case 'section':
                    case 'div':
                        if (el.querySelector('h1, h2, h3, h4, h5, h6')) {
                            result.structuredContent = this.extractArticleData(el);
                        }
                        break;
                    case 'ul':
                    case 'ol':
                        result.structuredContent = this.extractListData(el);
                        break;
                    case 'img':
                        result.structuredContent = this.extractImageData(el);
                        break;
                    case 'a':
                        result.structuredContent = this.extractLinkData(el);
                        break;
                    case 'video':
                    case 'audio':
                        result.structuredContent = this.extractMediaData(el);
                        break;
                }

                // Add metadata
                result.metadata = {
                    elementCount: el.querySelectorAll('*').length,
                    textLength: result.text.length,
                    hasChildren: el.children.length > 0,
                    isVisible: !!(el.offsetWidth || el.offsetHeight || el.getClientRects().length),
                    position: {
                        top: el.getBoundingClientRect().top,
                        left: el.getBoundingClientRect().left,
                        width: el.getBoundingClientRect().width,
                        height: el.getBoundingClientRect().height
                    }
                };

                return result;

                // Helper functions for specific content types
                function extractTableData(table) {
                    const data = { headers: [], rows: [] };
                    const headerCells = table.querySelectorAll('th');
                    data.headers = Array.from(headerCells).map(cell => cell.textContent?.trim() || '');
                    
                    const rows = table.querySelectorAll('tbody tr, tr:not(:first-child)');
                    data.rows = Array.from(rows).map(row => {
                        const cells = row.querySelectorAll('td, th');
                        return Array.from(cells).map(cell => cell.textContent?.trim() || '');
                    });
                    return data;
                }

                function extractFormData(form) {
                    const data = { fields: [], action: form.action || '', method: form.method || 'GET' };
                    const inputs = form.querySelectorAll('input, textarea, select');
                    data.fields = Array.from(inputs).map(input => ({
                        name: input.name || input.id || '',
                        type: input.type || input.tagName.toLowerCase(),
                        value: input.value || '',
                        placeholder: input.placeholder || '',
                        required: input.required,
                        label: this.findLabelForInput(input)
                    }));
                    return data;
                }

                function findLabelForInput(input) {
                    let label = '';
                    if (input.id) {
                        const labelEl = document.querySelector(`label[for="${input.id}"]`);
                        if (labelEl) label = labelEl.textContent?.trim() || '';
                    }
                    if (!label && input.closest('label')) {
                        label = input.closest('label').textContent?.trim() || '';
                    }
                    return label;
                }

                function extractArticleData(article) {
                    const data = { headings: [], paragraphs: [], links: [] };
                    
                    // Extract headings
                    const headings = article.querySelectorAll('h1, h2, h3, h4, h5, h6');
                    data.headings = Array.from(headings).map(h => ({
                        level: parseInt(h.tagName.substring(1)),
                        text: h.textContent?.trim() || ''
                    }));

                    // Extract paragraphs
                    const paragraphs = article.querySelectorAll('p');
                    data.paragraphs = Array.from(paragraphs)
                        .map(p => p.textContent?.trim() || '')
                        .filter(text => text.length > 0);

                    // Extract links
                    const links = article.querySelectorAll('a[href]');
                    data.links = Array.from(links).map(link => ({
                        text: link.textContent?.trim() || '',
                        href: link.href,
                        external: !link.href.startsWith(window.location.origin)
                    }));

                    return data;
                }

                function extractListData(list) {
                    const items = list.querySelectorAll('li');
                    return {
                        type: list.tagName.toLowerCase(),
                        items: Array.from(items).map(item => item.textContent?.trim() || '')
                    };
                }

                function extractImageData(img) {
                    return {
                        src: img.src,
                        alt: img.alt || '',
                        title: img.title || '',
                        width: img.naturalWidth || img.width,
                        height: img.naturalHeight || img.height,
                        loaded: img.complete && img.naturalWidth > 0
                    };
                }

                function extractLinkData(link) {
                    return {
                        href: link.href,
                        text: link.textContent?.trim() || '',
                        target: link.target || '_self',
                        external: !link.href.startsWith(window.location.origin),
                        download: link.download || null
                    };
                }

                function extractMediaData(media) {
                    return {
                        src: media.src,
                        duration: media.duration || 0,
                        currentTime: media.currentTime || 0,
                        paused: media.paused,
                        muted: media.muted,
                        volume: media.volume,
                        controls: media.hasAttribute('controls')
                    };
                }
            }, {
                tagName: element.tagName,
                type: element.type,
                attributes: element.attributes
            });

            // Format the extracted content for better readability
            return this.formatExtractedContent(extractedData);

        } catch (error) {
            console.error('Error extracting content:', error);
            // Fallback to simple text extraction
            const fallbackText = await elementHandle.textContent();
            return fallbackText || 'Unable to extract content from this element.';
        }
    }

    private formatExtractedContent(data: any): string {
        const lines = [];
        
        lines.push(`=== EXTRACTED CONTENT (${data.type.toUpperCase()}) ===`);
        
        if (data.text) {
            lines.push(`\nText Content:\n${data.text}`);
        }

        if (Object.keys(data.attributes).length > 0) {
            lines.push(`\nAttributes:`);
            for (const [key, value] of Object.entries(data.attributes)) {
                lines.push(`  ${key}: ${value}`);
            }
        }

        if (Object.keys(data.structuredContent).length > 0) {
            lines.push(`\nStructured Content:`);
            const structured = data.structuredContent;

            if (structured.headers && structured.rows) {
                // Table data
                lines.push(`  Table with ${structured.headers.length} columns and ${structured.rows.length} rows`);
                if (structured.headers.length > 0) {
                    lines.push(`  Headers: ${structured.headers.join(' | ')}`);
                }
                structured.rows.slice(0, 5).forEach((row: string[], index: number) => {
                    lines.push(`  Row ${index + 1}: ${row.join(' | ')}`);
                });
                if (structured.rows.length > 5) {
                    lines.push(`  ... and ${structured.rows.length - 5} more rows`);
                }
            } else if (structured.fields) {
                // Form data
                lines.push(`  Form (${structured.method} ${structured.action})`);
                structured.fields.forEach((field: any) => {
                    lines.push(`    ${field.label || field.name}: ${field.type}${field.required ? ' (required)' : ''}`);
                });
            } else if (structured.headings) {
                // Article data
                lines.push(`  Article Structure:`);
                structured.headings.forEach((heading: any) => {
                    lines.push(`    H${heading.level}: ${heading.text}`);
                });
                if (structured.paragraphs.length > 0) {
                    lines.push(`    ${structured.paragraphs.length} paragraphs`);
                }
                if (structured.links.length > 0) {
                    lines.push(`    ${structured.links.length} links`);
                }
            } else if (structured.items) {
                // List data
                lines.push(`  ${structured.type.toUpperCase()} with ${structured.items.length} items:`);
                structured.items.slice(0, 10).forEach((item: string, index: number) => {
                    lines.push(`    ${index + 1}. ${item}`);
                });
                if (structured.items.length > 10) {
                    lines.push(`    ... and ${structured.items.length - 10} more items`);
                }
            }
        }

        if (data.metadata) {
            lines.push(`\nMetadata:`);
            lines.push(`  Element count: ${data.metadata.elementCount}`);
            lines.push(`  Text length: ${data.metadata.textLength} characters`);
            lines.push(`  Visible: ${data.metadata.isVisible}`);
            lines.push(`  Position: ${Math.round(data.metadata.position.left)}, ${Math.round(data.metadata.position.top)}`);
            lines.push(`  Size: ${Math.round(data.metadata.position.width)} x ${Math.round(data.metadata.position.height)}`);
        }

        lines.push(`\n=== END EXTRACTED CONTENT ===`);
        
        return lines.join('\n');
    }

    async scrollDown(amount: number) {
        const page = await this.getCurrentPage();
        if (amount > 0) {
            await page.evaluate(`window.scrollBy(0, ${amount})`);
        } else {
            await page.evaluate(`window.scrollBy(0, window.innerHeight)`);
        }
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async scrollUp(amount: number) {
        const page = await this.getCurrentPage();
        if (amount > 0) {
            await page.evaluate(`window.scrollBy(0, -${amount})`);
        } else {
            await page.evaluate(`window.scrollBy(0, -window.innerHeight)`);
        }
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async scrollToElement() {

    }

    async sendKeys(keys: string) {
        const page = await this.getCurrentPage();
        await page.keyboard.press(keys);
        await this.waitForLoadState();
        await this.resetActiveElements();
    }

    async getDropdownOptions() {

    }

    async selectDropdownOption() {

    }

    private buildBaseLocator(element: DomElementNode) {
        let selector = this.convertXpathToSelector(element.xpath);

        console.log('Element Attributes', element);
        
        console.log('Selector', selector);

        if ('class' in element.attributes && element.attributes.class.length > 0) {
            const validClassNamePattern = /^[a-zA-Z_][a-zA-Z0-9_-]*$/;
            const classNames = element.attributes.class.split(' ');
            for (const className of classNames) {
                if (validClassNamePattern.test(className)) {
                    selector += `.${className}`;
                }
            }
        }

        for (const attribute in element.attributes) {
            const value = element.attributes[attribute];
            if (value === 'class') {
                continue;
            }
            if (value.trim() !== value) {
                continue;
            }
            if (!this.safeAttributes.includes(attribute)) {
                continue;
            }
            const safeAttribute = attribute.replace(':', '\:')
            if (value === '') {
                selector += `[${safeAttribute}]`;
            } else {
                selector += `[${safeAttribute}="${value}"]`;
            }
        }

        return selector;
    }

    private convertXpathToSelector(xpath: string) {
        if (!xpath) {
            return '';
        }
        xpath = xpath.trimStart();
        const parts = xpath.split('/');
        const cssParts: string[] = [];

        for (const part of parts) {
            if (!part) {
                continue;
            }

            if (part.includes('[')) {
                const bracketIndex = part.indexOf('[');
                let basePart = part.substring(0, bracketIndex);
                const indexPart = part.substring(bracketIndex);

                console.log('Index Part', indexPart);
                const indices = indexPart
                    .split(']')
                    .slice(0, -1) // 移除最后的空字符串
                    .map(i => i.replace(/[\[\]]/g, '').trim());
                console.log('Indices', indices);
                for (const idx of indices) {
                    console.log('Index', idx);
                    if (/^\d+$/.test(idx)) {
                        const index = parseInt(idx);
                        basePart += `:nth-of-type(${index})`;
                    } else if (idx === 'last())') {
                        basePart += ':last-of-type';
                    } else if (idx.includes('position()>1')) {
                        basePart += ':nth-child(n+2)';
                    }
                    cssParts.push(basePart);
                }
            } else {
                cssParts.push(part);
            }
        }

        return cssParts.join(' > ');
    }

    async getDetailedPageInfo() {
        const page = await this.getCurrentPage();
        if (!page) {
            throw new Error('No active page found');
        }

        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                url: window.location.href,
                domain: window.location.hostname,
                protocol: window.location.protocol,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                scroll: {
                    x: window.pageXOffset || document.documentElement.scrollLeft,
                    y: window.pageYOffset || document.documentElement.scrollTop
                },
                document: {
                    readyState: document.readyState,
                    contentType: document.contentType,
                    lastModified: document.lastModified,
                    characterSet: document.characterSet
                },
                performance: {
                    loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                    domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
                },
                meta: {
                    description: (document.querySelector('meta[name="description"]') as HTMLMetaElement)?.content || '',
                    keywords: (document.querySelector('meta[name="keywords"]') as HTMLMetaElement)?.content || '',
                    author: (document.querySelector('meta[name="author"]') as HTMLMetaElement)?.content || '',
                    viewport: (document.querySelector('meta[name="viewport"]') as HTMLMetaElement)?.content || ''
                },
                elements: {
                    total: document.querySelectorAll('*').length,
                    interactive: document.querySelectorAll('button, input, select, textarea, a[href]').length,
                    images: document.querySelectorAll('img').length,
                    links: document.querySelectorAll('a[href]').length,
                    forms: document.querySelectorAll('form').length,
                    tables: document.querySelectorAll('table').length
                }
            };
        });

        return {
            ...pageInfo,
            tabs: await this.getTabInfo(),
            timestamp: new Date().toISOString()
        };
    }

    async extractAllPageContent() {
        const page = await this.getCurrentPage();
        if (!page) {
            throw new Error('No active page found');
        }

        const content = await page.evaluate(() => {
            const result = {
                headings: [],
                paragraphs: [],
                links: [],
                images: [],
                forms: [],
                tables: [],
                lists: [],
                text: ''
            };

            // Extract headings
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            result.headings = Array.from(headings).map(h => ({
                level: parseInt(h.tagName.substring(1)),
                text: h.textContent?.trim() || '',
                id: h.id || null
            }));

            // Extract paragraphs
            const paragraphs = document.querySelectorAll('p');
            result.paragraphs = Array.from(paragraphs)
                .map(p => p.textContent?.trim() || '')
                .filter(text => text.length > 0);

            // Extract links
            const links = document.querySelectorAll('a[href]');
            result.links = Array.from(links).map(link => ({
                text: link.textContent?.trim() || '',
                href: (link as HTMLAnchorElement).href,
                external: !(link as HTMLAnchorElement).href.startsWith(window.location.origin)
            }));

            // Extract images
            const images = document.querySelectorAll('img');
            result.images = Array.from(images).map(img => ({
                src: (img as HTMLImageElement).src,
                alt: (img as HTMLImageElement).alt || '',
                title: (img as HTMLImageElement).title || ''
            }));

            // Extract forms
            const forms = document.querySelectorAll('form');
            result.forms = Array.from(forms).map(form => {
                const inputs = form.querySelectorAll('input, textarea, select');
                return {
                    action: (form as HTMLFormElement).action || '',
                    method: (form as HTMLFormElement).method || 'GET',
                    fieldCount: inputs.length,
                    fields: Array.from(inputs).map(input => ({
                        name: (input as HTMLInputElement).name || (input as HTMLInputElement).id || '',
                        type: (input as HTMLInputElement).type || input.tagName.toLowerCase(),
                        placeholder: (input as HTMLInputElement).placeholder || ''
                    }))
                };
            });

            // Extract tables
            const tables = document.querySelectorAll('table');
            result.tables = Array.from(tables).map(table => {
                const rows = table.querySelectorAll('tr');
                const headers = table.querySelectorAll('th');
                return {
                    rowCount: rows.length,
                    columnCount: headers.length || (rows[0]?.querySelectorAll('td, th').length || 0),
                    hasHeaders: headers.length > 0
                };
            });

            // Extract lists
            const lists = document.querySelectorAll('ul, ol');
            result.lists = Array.from(lists).map(list => {
                const items = list.querySelectorAll('li');
                return {
                    type: list.tagName.toLowerCase(),
                    itemCount: items.length,
                    items: Array.from(items).slice(0, 5).map(item => item.textContent?.trim() || '')
                };
            });

            // Extract all text content
            result.text = document.body.textContent?.trim() || '';

            return result;
        });

        return {
            ...content,
            url: await this.getCurrentUrl(),
            timestamp: new Date().toISOString(),
            wordCount: content.text.split(/\s+/).filter(word => word.length > 0).length
        };
    }

    async searchTextOnPage(searchTerm: string, caseSensitive: boolean = false) {
        const page = await this.getCurrentPage();
        if (!page) {
            throw new Error('No active page found');
        }

        const results = await page.evaluate((term, sensitive) => {
            const searchText = sensitive ? term : term.toLowerCase();
            const matches = [];
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null
            );

            let node;
            while (node = walker.nextNode()) {
                const textContent = sensitive ? node.textContent : node.textContent.toLowerCase();
                if (textContent.includes(searchText)) {
                    const parent = node.parentElement;
                    const rect = parent.getBoundingClientRect();
                    
                    matches.push({
                        text: node.textContent.trim(),
                        element: parent.tagName.toLowerCase(),
                        position: {
                            x: rect.left,
                            y: rect.top,
                            width: rect.width,
                            height: rect.height
                        },
                        visible: rect.width > 0 && rect.height > 0,
                        context: this.getContextAround(node.textContent, term, 50)
                    });
                }
            }

            return matches;

            function getContextAround(text, searchTerm, contextLength) {
                const index = text.toLowerCase().indexOf(searchTerm.toLowerCase());
                if (index === -1) return text;
                
                const start = Math.max(0, index - contextLength);
                const end = Math.min(text.length, index + searchTerm.length + contextLength);
                
                return (start > 0 ? '...' : '') + 
                       text.substring(start, end) + 
                       (end < text.length ? '...' : '');
            }
        }, searchTerm, caseSensitive);

        return {
            searchTerm,
            caseSensitive,
            matchCount: results.length,
            matches: results,
            url: await this.getCurrentUrl(),
            timestamp: new Date().toISOString()
        };
    }
}

