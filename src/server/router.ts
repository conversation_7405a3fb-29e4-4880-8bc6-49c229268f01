import { IncomingMessage, ServerResponse } from 'http';
import { TaskManager } from './controller/task';

type Handler = (req: IncomingMessage, res: ServerResponse) => Promise<void>;
type RouteMap = Record<string, Handler>;

export const router: RouteMap = {
    '/task/create': async (_req, res) => {
        try {
            const body = await getRequestBody(_req);
            const parsedBody = JSON.parse(body);
            const taskManager = TaskManager.getInstance();
            await taskManager.init();
            const result = await taskManager.createTask(parsedBody);
            sendJson(res, 200, { result: result });
        } catch (error) {
            console.error('Error in /task/create:', error);
            sendJson(res, 500, { error: error.message });
        }
    },
    '/task/execute': async (_req, res) => {
        try {
            const body = await getRequestBody(_req);
            const parsedBody = JSON.parse(body);
            const taskManager = TaskManager.getInstance();
            const result = await taskManager.executeTask(parsedBody);
            sendJson(res, 200, { result: result });
        } catch (error) {
            console.error('Error in /task/execute:', error);
            sendJson(res, 500, { error: error.message });
        }
    },
    '/test': async (_req, res) => {
        sendJson(res, 200, { message: 'CORS test successful!', timestamp: new Date().toISOString() });
    },
    '/browser/screenshot': async (_req, res) => {
        try {
            const body = await getRequestBody(_req);
            const parsedBody = JSON.parse(body);
            const taskManager = TaskManager.getInstance();
            const result = await taskManager.takeScreenshot(parsedBody);
            sendJson(res, 200, { result: result });
        } catch (error) {
            console.error('Error in /browser/screenshot:', error);
            sendJson(res, 500, { error: error.message });
        }
    },
    '/browser/page-info': async (_req, res) => {
        try {
            const body = await getRequestBody(_req);
            const parsedBody = JSON.parse(body);
            const taskManager = TaskManager.getInstance();
            const result = await taskManager.getPageInfo(parsedBody);
            sendJson(res, 200, { result: result });
        } catch (error) {
            console.error('Error in /browser/page-info:', error);
            sendJson(res, 500, { error: error.message });
        }
    },
    '/browser/extract-all': async (_req, res) => {
        try {
            const body = await getRequestBody(_req);
            const parsedBody = JSON.parse(body);
            const taskManager = TaskManager.getInstance();
            const result = await taskManager.extractAllContent(parsedBody);
            sendJson(res, 200, { result: result });
        } catch (error) {
            console.error('Error in /browser/extract-all:', error);
            sendJson(res, 500, { error: error.message });
        }
    },
    '/browser/search-text': async (_req, res) => {
        try {
            const body = await getRequestBody(_req);
            const parsedBody = JSON.parse(body);
            const taskManager = TaskManager.getInstance();
            const result = await taskManager.searchText(parsedBody);
            sendJson(res, 200, { result: result });
        } catch (error) {
            console.error('Error in /browser/search-text:', error);
            sendJson(res, 500, { error: error.message });
        }
    }
}

const sendJson = (res: ServerResponse, status: number, body: unknown) => {
    res.statusCode = status;
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    // CORS头部已在主服务器中设置，这里不需要重复设置
    res.end(JSON.stringify(body));
}

async function getRequestBody(req: IncomingMessage): Promise<string> {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', (chunk) => {
          body += chunk.toString(); // 拼接数据块
        });
        req.on('end', () => {
          resolve(body); // 返回完整 body
        });
        req.on('error', (err) => {
          reject(err); // 处理错误
        });
      });
}