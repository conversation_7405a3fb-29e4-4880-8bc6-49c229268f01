import "dotenv/config"; 
import http, { IncomingMessage, ServerResponse } from 'http';
import { AgentOverChromeBridge } from '@midscene/web/bridge-mode';
import { Context } from './context';
import fs from 'fs';
import path from 'path';
import { router } from './router';

console.log(AgentOverChromeBridge);

// 保持与原 BrowserAgent 兼容的配置接口
interface BrowserConfig {
    cdpUrl: string;
    remoteDebuggingPort: number;
    userDataDir: string;
    disableSecurity: boolean;
    extraChromeArgs: string[];
    headless: boolean;
    chromeInstancePath: string;
}

export class AgentHttpServer {
    private port: number;
    private publicDir: string;
    private browserAgent: AgentOverChromeBridge | null;
    private config: BrowserConfig | null;

    async init(port: number, browserConfig?: BrowserConfig) {
        this.port = port;
        this.publicDir = path.join(process.cwd(), 'public');

        // 保存配置（虽然 bridge 模式不需要这些配置，但保持 API 兼容性）
        this.config = browserConfig || {
            cdpUrl: 'http://localhost:9222',
            remoteDebuggingPort: 9222,
            userDataDir: '',
            disableSecurity: true,
            extraChromeArgs: [],
            headless: false, // bridge 模式总是使用用户的可见浏览器
            chromeInstancePath: '',
        };

        // 创建 AgentOverChromeBridge 实例
        this.browserAgent = new AgentOverChromeBridge({
            // 可选配置：是否在断开连接后关闭新创建的标签页
            closeNewTabsAfterDisconnect: false,
        });
    }

    // 连接到当前活动的浏览器标签页
    async connectToCurrentTab() {
        if (!this.browserAgent) {
            throw new Error('Browser agent not initialized. Call init() first.');
        }
        await this.browserAgent.connectCurrentTab();
        console.log('Connected to current active tab');
    }

    // 连接到新的浏览器标签页并打开指定 URL
    async connectToNewTab(url: string) {
        if (!this.browserAgent) {
            throw new Error('Browser agent not initialized. Call init() first.');
        }
        await this.browserAgent.connectNewTabWithUrl(url);
        console.log(`Connected to new tab with URL: ${url}`);
    }

    // 获取浏览器代理实例（用于 AI 操作）
    getBrowserAgent(): AgentOverChromeBridge | null {
        return this.browserAgent;
    }

    // 清理连接
    async destroy() {
        if (this.browserAgent) {
            await this.browserAgent.destroy();
            console.log('Browser agent connection destroyed');
        }
    }

    async start() {
        http.createServer(async (req: IncomingMessage, res: ServerResponse) => {
            try {
                // 设置CORS头部
                this.setCorsHeaders(res);

                // 处理预检请求
                if (req.method === 'OPTIONS') {
                    res.statusCode = 200;
                    res.end();
                    return;
                }

                const path = this.normalizePath(req.url);
                if (await this.tryServeHtml(path, res)) return;
                const handler = router[path];
                if (handler) {
                    await handler(req, res);
                } else {
                    this.sendJson(res, 404, { error: 'Not Found' });
                }
            } catch (err) {
                this.sendJson(res, 500, { error: (err as Error).message });
            }
        })
        .listen(this.port, () => {
            console.log(`Server running at http://localhost:${this.port}`);
        });
    }

    normalizePath (u?: string | null) {
        if (!u) return '/';
        const i = u.indexOf('?');
        return (i >= 0 ? u.slice(0, i) : u) || '/';
      }
      
      
    setCorsHeaders(res: ServerResponse) {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        res.setHeader('Access-Control-Max-Age', '86400');
    }

    async sendJson (res: ServerResponse, status: number, body: unknown) {
        res.statusCode = status;
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        res.end(JSON.stringify(body));
    }

    async tryServeHtml (requestPath: string, res: ServerResponse) {
        const safeFilePath = this.resolveHtmlPath(requestPath);
        if (!safeFilePath) return false;
        try {
          const data = await fs.promises.readFile(safeFilePath);
          res.statusCode = 200;
          res.setHeader('Content-Type', 'text/html; charset=utf-8');
          res.end(data);
          return true;
        } catch {
          return false;
        }
      }

    resolveHtmlPath (requestPath: string) {
        const trimmed = requestPath || '/';
        const target = trimmed === '/' ? 'index.html' : trimmed.replace(/^\//, '');
        if (!target.endsWith('.html')) return null;
        const abs = path.join(this.publicDir, target);
        // Prevent path traversal outside PUBLIC_DIR
        if (!abs.startsWith(this.publicDir)) return null;
        return abs;
      }
}