import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import process from 'process';
import fs from 'fs';
import {
    chromium,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON> as PlaywrightPage,
    <PERSON><PERSON>er<PERSON>ontext as PlaywrightContext
} from 'playwright';

interface BrowserConfig {
    cdpUrl: string;
    remoteDebuggingPort: number;
    userDataDir: string;
    disableSecurity: boolean;
    extraChromeArgs: string[];
    headless: boolean;
    chromeInstancePath: string;
}

export class BrowserAgent {

    browser: PlaywrightBrowser | null;
    context: PlaywrightContext | null;
    page: PlaywrightPage | null;
    config: BrowserConfig | null;
    chromeProcess: ChildProcess | null;
    cdpUrl: string | null;

    async init(config: BrowserConfig) {
        this.config = config;
    }

    async setupBrowserWithChromeCdp() {
        const chromePath = await this.findBestExecutableChrome();
        console.log('chromePath', chromePath);
    }

    async findBestExecutableChrome() {
        const paths: string[] = [];

        if (process.platform === 'darwin') {
            // macOS paths
            paths.push('/Applications/Google Chrome.app/Contents/MacOS/Google Chrome');
            paths.push('/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta');
            paths.push('/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev');
            paths.push('/Applications/Chromium.app/Contents/MacOS/Chromium');
            // Add fallback for homebrew installations
            paths.push('/opt/homebrew/bin/chromium');
        } else if (process.platform === 'win32') {
            // Windows paths
            paths.push('C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe');
            paths.push('C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe');
            
            // Add LocalAppData path if available
            const localAppData = process.env.LOCALAPPDATA;
            if (localAppData) {
              paths.push(`${localAppData}\\Google\\Chrome\\Application\\chrome.exe`);
            }
            
            // Microsoft Edge as fallback (Chromium-based)
            paths.push('C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe');
            paths.push('C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe');
        } else {
            // Linux paths
            paths.push('/usr/bin/google-chrome');
            paths.push('/usr/bin/google-chrome-stable');
            paths.push('/opt/google/chrome/chrome');
            paths.push('/usr/bin/chromium-browser');
            paths.push('/usr/bin/chromium');
            paths.push('/snap/bin/chromium');
        }
          
        console.log('Checking for Chrome at these paths:', paths);
        
        // Find first existing path
        for (const path of paths) {
        if (fs.existsSync(path)) {
            console.log(`Found Chrome executable at: ${path}`);
            return path;
        }
        }
        
        console.error('No Chrome executable found in common locations');
        return null;
    }

    async getUserDataDir() {
        return path.join(process.cwd(), 'userData');
    }

    async verifyChromeLaunched() {
        try {
            const response = await fetch('http://localhost:9222/json/version');
            if (!response.ok) {
                throw new Error('Chrome not launched');
            }
            console.log('response', response);
            const responseData = await response.json() as any;
            this.cdpUrl = responseData.webSocketDebuggerUrl;
            return true;
        } catch (error) {
            return false;
        }
    }

    async startChromeProcess() {
        const chromeLaunched = await this.verifyChromeLaunched();

        if (!chromeLaunched) {
            const chromePath = await this.findBestExecutableChrome();
            if (!chromePath) {
                console.error('No Chrome executable found');
                throw new Error('Chrome executable not found');
            }

            const userDataDir = await this.getUserDataDir();

            this.chromeProcess = spawn(chromePath, ['--remote-debugging-port=9222', '--user-data-dir=' + userDataDir]);
            console.log('Chrome process started');
            await sleep(2000); // 增加等待时间
        } else {
            console.log('Chrome already launched');
        }

        // 无论Chrome是否已经在运行，都尝试连接
        try {
            if (!this.browser) {
                this.browser = await chromium.connectOverCDP('http://localhost:9222');
                console.log('Browser Connected.');
            }
        } catch (error) {
            console.error('Failed to connect to Chrome:', error);
            throw error;
        }
    }

    async closeChromeProcess() {
        if (this.chromeProcess) {
            this.chromeProcess.kill('SIGKILL');
        }
    }
    
}

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

