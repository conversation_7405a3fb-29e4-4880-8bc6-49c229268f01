/**
 * This file will automatically be loaded by vite and run in the "renderer" context.
 * To learn more about the differences between the "main" and the "renderer" context in
 * Electron, visit:
 *
 * https://electronjs.org/docs/tutorial/process-model
 */

import './index.css';
import { serverCommand, chromeCommand } from './config';

// Define interface for Python bridge
interface PyBridge {
  onPyOutput: (callback: (data: { type: string; data: string }) => void) => void;
  onPyStarted: (callback: () => void) => void;
  onPyReady: (callback: (url: string) => void) => void;
  restartPython: () => void;
  removeAllListeners: () => void;
}

// Define interface for Chrome bridge
interface ChromeBridge {
  onChromeOutput: (callback: (data: { type: string; data: string }) => void) => void;
  onChromeStarted: (callback: () => void) => void;
  restartChrome: () => void;
  removeAllListeners: () => void;
}

// Access the exposed API from preload script
declare global {
  interface Window {
    pyBridge: PyBridge;
    chromeBridge: ChromeBridge;
    settingsBridge: {
      onDefaultSettings: (callback: (settings: any) => void) => void;
    };
    electronAPI: {
      minimize: () => void;
      toggleMaximize: () => void;
      close: () => void;
    };
  }
}

// DOM Elements
const webviewContainer = document.getElementById('webview-container') as HTMLDivElement;
const webview = document.getElementById('webview') as HTMLElement;

// Variables to track loading
let serverUrl = '';

// Function to load the web UI
function loadWebUI(url: string): void {
  // Force the correct type for webview element
  const webviewElement = document.querySelector('webview') as any;
  if (webviewElement) {
    // Add loading indicator
    showLoadingIndicator();
    
    webviewElement.src = url;
    
    // Handle webview events
    webviewElement.addEventListener('dom-ready', () => {
      console.log('WebView DOM ready');
      // Inject performance optimizations
      webviewElement.executeJavaScript(`
        // Optimize scrolling performance
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Add CSS for better rendering
        const style = document.createElement('style');
        style.textContent = \`
          * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
          body {
            transform: translateZ(0);
            backface-visibility: hidden;
          }
        \`;
        document.head.appendChild(style);
      `);
    });
    
    webviewElement.addEventListener('did-start-loading', () => {
      console.log('WebView started loading');
      showLoadingIndicator();
    });
    
    webviewElement.addEventListener('did-finish-load', () => {
      console.log('WebView finished loading');
      hideLoadingIndicator();
      // Remove loading class when webview is loaded
      document.body.classList.remove('loading');
    });
    
    // Handle loading failures
    webviewElement.addEventListener('did-fail-load', (event: any) => {
      console.error('WebView failed to load:', event);
      hideLoadingIndicator();
      showErrorMessage('Failed to load content. Please check your connection.');
    });
    
    // Optimize navigation
    webviewElement.addEventListener('will-navigate', (event: any) => {
      console.log('WebView will navigate to:', event.url);
    });
  }
  
  // Show the webview and controls, hide other elements
  webviewContainer.style.display = 'block';
}

// Loading indicator functions
function showLoadingIndicator(): void {
  const indicator = document.getElementById('loading-indicator') || createLoadingIndicator();
  indicator.style.display = 'block';
}

function hideLoadingIndicator(): void {
  const indicator = document.getElementById('loading-indicator');
  if (indicator) {
    indicator.style.display = 'none';
  }
}

function createLoadingIndicator(): HTMLElement {
  const indicator = document.createElement('div');
  indicator.id = 'loading-indicator';
  indicator.className = 'loading-spinner';
  indicator.innerHTML = `
    <div class="spinner"></div>
    <div class="loading-text">Loading...</div>
  `;
  document.body.appendChild(indicator);
  return indicator;
}

function showErrorMessage(message: string): void {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.textContent = message;
  document.body.appendChild(errorDiv);
  
  setTimeout(() => {
    document.body.removeChild(errorDiv);
  }, 5000);
}

// Window control functions
function setupWindowControls(): void {
  const minimizeBtn = document.getElementById('minimize-btn');
  const maximizeBtn = document.getElementById('maximize-btn');
  const closeBtn = document.getElementById('close-btn');

  if (minimizeBtn) {
    minimizeBtn.addEventListener('click', () => {
      if (window.electronAPI?.minimize) {
        window.electronAPI.minimize();
      }
    });
  }

  if (maximizeBtn) {
    maximizeBtn.addEventListener('click', () => {
      if (window.electronAPI?.toggleMaximize) {
        window.electronAPI.toggleMaximize();
      }
    });
  }

  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      if (window.electronAPI?.close) {
        window.electronAPI.close();
      }
    });
  }
}

// Initialize the app
function init(): void {
  serverUrl = 'https://global-service-frontend-rcn-test.smartdeer.tech/n/icb_test/app/rcn/chat-agent';
  setupWindowControls();
  loadWebUI(serverUrl);
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', init);