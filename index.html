<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>SmartDeer Desktop</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        overflow: hidden;
        user-select: none;
        -webkit-app-region: drag;
      }
      h1 {
        margin-bottom: 20px;
        color: #2c3e50;
      }
      main {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .console-output {
        background-color: #263238;
        color: #eeffff;
        padding: 0;
        border-radius: 5px 5px 0 0;
        height: 300px;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 15;
        transform: translateY(100%);
        transition: transform 0.3s ease;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.3);
        display: flex;
        flex-direction: column;
      }
      
      .console-output.visible {
        transform: translateY(0);
      }
      
      /* Adjust layout for loading state */
      body.loading .console-output.visible {
        height: 250px;
      }
      
      .console-header {
        background-color: #1e272c;
        padding: 8px 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #37474F;
      }
      
      .console-header h3 {
        margin: 0;
        font-size: 14px;
        font-weight: normal;
        color: #B0BEC5;
      }
      
      .console-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .console-tabs {
        display: flex;
        gap: 8px;
      }
      
      .console-tab {
        background-color: #37474F;
        border: none;
        color: #B0BEC5;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }
      
      .console-tab.active {
        background-color: #546E7A;
        color: #ECEFF1;
      }
      
      .console-tab:hover {
        background-color: #455A64;
      }
      
      #console-actions {
        background-color: #1e272c;
        padding: 8px 15px;
        display: flex;
        border-bottom: 1px solid #37474F;
      }
      
      .restart-btn {
        background-color: #455A64;
        color: #ECEFF1;
        border: none;
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 12px;
        cursor: pointer;
        margin-right: 10px;
      }
      
      .restart-btn:hover {
        background-color: #546E7A;
      }
      
      .action-row {
        display: flex;
        align-items: center;
        width: 100%;
      }
      
      .command-display {
        margin-left: 15px;
        font-family: monospace;
        color: #81C784;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        font-size: 12px;
      }
      
      .console-content {
        overflow-y: auto;
        padding: 15px;
        font-family: monospace;
        white-space: pre-wrap;
        flex: 1;
        height: 100%;
      }
      
      .command-line {
        color: #81C784;
        font-weight: bold;
        padding: 5px 0;
        border-bottom: 1px solid #37474F;
        margin-bottom: 10px;
      }
      
      .close-btn {
        font-size: 24px;
        color: #B0BEC5;
        cursor: pointer;
        user-select: none;
        line-height: 1;
      }
      
      .close-btn:hover {
        color: #ECEFF1;
      }
      
      #console-content {
        overflow-y: auto;
        padding: 15px;
        font-family: monospace;
        white-space: pre-wrap;
        flex: 1;
      }
      .console-output .stderr {
        color: #ff5252;
      }
      .console-output .info {
        color: #69f0ae;
      }
      .console-output .error {
        color: #ff7043;
      }
      .loading-container {
        margin: 20px 0;
      }
      .loading-bar {
        height: 20px;
        background-color: #ddd;
        border-radius: 10px;
        overflow: hidden;
      }
      .loading-progress {
        height: 100%;
        background-color: #4caf50;
        width: 0%;
        transition: width 0.3s ease;
      }
      .webview-container {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        padding-top: 20px;
      }
      
      .controls {
        position: fixed;
        bottom: 10px;
        right: 10px;
        z-index: 20;
        background-color: rgba(0,0,0,0.6);
        border-radius: 5px;
        padding: 8px;
        display: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        transition: opacity 0.3s ease;
      }
      
      .controls:hover {
        opacity: 1;
      }
      
      .controls button {
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 10px 15px;
        cursor: pointer;
        font-weight: bold;
        transition: background-color 0.2s ease;
      }
      
      .controls button:hover {
        background-color: #0d8aee;
      }
      webview {
        width: 100%;
        height: 100%;
        border: none;
      }
      .launch-button {
        display: none;
        padding: 10px 20px;
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 10px;
      }
      .launch-button:hover {
        background-color: #1976d2;
      }
      
      /* App header and logo */
      .app-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 40px;
        background: rgba(0,0,0,0.8);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        padding: 0 15px;
        z-index: 100;
        -webkit-app-region: drag;
        border-bottom: 1px solid rgba(255,255,255,0.1);
      }
      
      .app-logo {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #fff;
        font-weight: 600;
        font-size: 14px;
      }
      
      .logo-icon {
        width: 20px;
        height: 20px;
        background: linear-gradient(45deg, #4facfe, #00f2fe);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 12px;
        color: white;
      }
      
      .window-controls {
        margin-left: auto;
        display: flex;
        gap: 8px;
        -webkit-app-region: no-drag;
      }
      
      .window-control {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
      
      .window-control svg {
        opacity: 0;
        transition: opacity 0.2s;
        color: rgba(0,0,0,0.8);
      }
      
      .window-control:hover {
        transform: scale(1.1);
      }
      
      .window-control:hover svg {
        opacity: 1;
      }
      
      .window-control.close {
        background-color: #ff5f57;
      }
      
      .window-control.minimize {
        background-color: #ffbd2e;
      }
      
      .window-control.maximize {
        background-color: #28ca42;
      }
      
      /* Loading spinner */
      .loading-spinner {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
        display: none;
        text-align: center;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid #4facfe;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
      }
      
      /* Error message */
      .error-message {
        position: fixed;
        top: 60px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 107, 107, 0.9);
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 1001;
        animation: slideDown 0.3s ease;
      }
      
      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }
      
      /* Adjust webview for header */
      .webview-container {
        display: none;
        position: absolute;
        top: 40px;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: calc(100% - 40px);
        z-index: 10;
        -webkit-app-region: no-drag;
      }
      
      /* Improve webview performance */
      webview {
        width: 100%;
        height: 100%;
        border: none;
        will-change: transform;
        transform: translateZ(0);
      }
    </style>
  </head>
  <body class="loading">
    <!-- App Header -->
    <div class="app-header">
      <div class="app-logo">
        <div class="logo-icon">S</div>
        <span>SmartDeer</span>
      </div>
      <div class="window-controls">
        <div class="window-control minimize" id="minimize-btn">
          <svg width="8" height="1" viewBox="0 0 8 1">
            <rect width="8" height="1" fill="currentColor"/>
          </svg>
        </div>
        <div class="window-control maximize" id="maximize-btn">
          <svg width="8" height="8" viewBox="0 0 8 8">
            <rect x="1" y="1" width="6" height="6" fill="none" stroke="currentColor" stroke-width="1"/>
          </svg>
        </div>
        <div class="window-control close" id="close-btn">
          <svg width="8" height="8" viewBox="0 0 8 8">
            <line x1="1" y1="1" x2="7" y2="7" stroke="currentColor" stroke-width="1.2"/>
            <line x1="7" y1="1" x2="1" y2="7" stroke="currentColor" stroke-width="1.2"/>
          </svg>
        </div>
      </div>
    </div>
    
    <main>
      <div id="webview-container" class="webview-container">
        <webview id="webview" src="about:blank" webpreferences="contextIsolation=yes" allowpopups></webview>
      </div>
    </main>
    
    <script type="module" src="/src/renderer.ts"></script>
  </body>
</html>
