{"name": "smartdeer-desktop", "productName": "SmartDeer Desktop", "version": "1.0.0", "description": "SmartDeer application on Desktop", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": {"name": "yong.yang", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.0", "@electron-forge/maker-deb": "^7.8.0", "@electron-forge/maker-rpm": "^7.8.0", "@electron-forge/maker-squirrel": "^7.8.0", "@electron-forge/maker-zip": "^7.8.0", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron/fuses": "^1.8.0", "@midscene/web": "^0.27.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "dotenv": "^17.2.2", "electron": "35.1.2", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "playwright": "1.47.2", "ts-node": "^10.9.2", "typescript": "~4.5.4", "uuid": "^11.0.5", "vite": "^5.4.15"}, "dependencies": {"electron-squirrel-startup": "^1.0.1"}}